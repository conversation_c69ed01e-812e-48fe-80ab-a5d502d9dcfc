import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/payment_methods_provider.dart';
import '../../models/payment_method.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../utils/app_colors.dart';

class PaymentMethodsDialog extends ConsumerWidget {
  const PaymentMethodsDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(paymentMethodsProvider);
    final notifier = ref.read(paymentMethodsProvider.notifier);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 제목
            _buildHeader(context, isTablet),

            SizedBox(height: isTablet ? 20.0 : 16.0),

            // 결제수단 목록
            _buildPaymentMethodsList(context, state, notifier, isTablet),

            SizedBox(height: isTablet ? 20.0 : 16.0),

            // 버튼
            _buildButtons(context, isTablet),
          ],
        ),
      ),
    );
  }

  /// 헤더 빌더
  Widget _buildHeader(BuildContext context, bool isTablet) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.payment_rounded,
          size: isTablet ? 20.0 : 18.0,
          color: AppColors.primarySeed,
        ),
        const SizedBox(width: 8.0),
        Text(
          '결제수단 관리',
          style: TextStyle(
            fontSize: isTablet ? 18.0 : 16.0,
            fontWeight: FontWeight.w600,
            color: AppColors.onboardingTextPrimary,
            fontFamily: 'Pretendard',
          ),
        ),
      ],
    );
  }

  /// 결제수단 목록 빌더
  Widget _buildPaymentMethodsList(BuildContext context, PaymentMethodsState state, PaymentMethodsNotifier notifier, bool isTablet) {
    return Column(
      children: state.methods.map((method) =>
        _buildPaymentMethodItem(context, method, notifier, isTablet)
      ).toList(),
    );
  }

  /// 결제수단 아이템 빌더
  Widget _buildPaymentMethodItem(BuildContext context, PaymentMethod method, PaymentMethodsNotifier notifier, bool isTablet) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: method.isActive
          ? AppColors.primarySeed.withValues(alpha: 0.05)
          : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: method.isActive
            ? AppColors.primarySeed.withValues(alpha: 0.2)
            : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: CheckboxListTile(
        title: Text(
          method.name,
          style: TextStyle(
            fontSize: isTablet ? 16.0 : 14.0,
            fontWeight: FontWeight.w500,
            color: method.isActive
              ? AppColors.onboardingTextPrimary
              : AppColors.onboardingTextSecondary,
            fontFamily: 'Pretendard',
          ),
        ),
        value: method.isActive,
        onChanged: (bool? value) {
          if (value != null) {
            notifier.toggleMethod(method.id);
          }
        },
        controlAffinity: ListTileControlAffinity.leading,
        activeColor: AppColors.primarySeed,
        contentPadding: EdgeInsets.symmetric(
          horizontal: isTablet ? 16.0 : 12.0,
          vertical: 4.0,
        ),
      ),
    );
  }

  /// 버튼 빌더
  Widget _buildButtons(BuildContext context, bool isTablet) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primarySeed,
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 20.0 : 16.0,
              vertical: isTablet ? 12.0 : 10.0,
            ),
          ),
          child: Text(
            '완료',
            style: TextStyle(
              fontSize: isTablet ? 16.0 : 14.0,
              fontWeight: FontWeight.w600,
              fontFamily: 'Pretendard',
            ),
          ),
        ),
      ],
    );
  }
}

